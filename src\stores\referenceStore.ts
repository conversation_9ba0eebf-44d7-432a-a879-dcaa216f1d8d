import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { getAmenities, getCostTypes, getRules, getAppEnums } from '@/actions/reference.action';

// Types for reference data
export interface Amenity {
  id: string;
  name: string;
  category: string;
  icon?: string;
}

export interface CostType {
  id: string;
  name: string;
  category: string;
  unit?: string;
}

export interface Rule {
  id: string;
  name: string;
  category: string;
  description?: string;
}

export interface AppEnums {
  roomTypes: string[];
  paymentStatuses: string[];
  genders: string[];
  roles: string[];
  propertyTypes: string[];
  contractStatuses: string[];
  verificationStatuses: string[];
}

// Vietnamese translations for enums
export const enumTranslations: Record<string, Record<string, string>> = {
  roomTypes: {
    'single': 'Phòng đơn',
    'double': 'Phòng đôi',
    'shared': 'Phòng chia sẻ',
    'studio': 'Căn hộ studio',
    'apartment': '<PERSON>ăn hộ',
    'house': '<PERSON>hà nguyên căn'
  },
  paymentStatuses: {
    'pending': 'Chờ thanh toán',
    'paid': 'Đã thanh toán',
    'overdue': 'Quá hạn',
    'cancelled': 'Đã hủy',
    'refunded': 'Đã hoàn tiền'
  },
  genders: {
    'male': 'Nam',
    'female': 'Nữ',
    'other': 'Khác'
  },
  roles: {
    'tenant': 'Người thuê',
    'landlord': 'Chủ trọ',
    'admin': 'Quản trị viên'
  },
  propertyTypes: {
    'room': 'Phòng trọ',
    'apartment': 'Căn hộ',
    'house': 'Nhà nguyên căn',
    'dormitory': 'Ký túc xá'
  },
  contractStatuses: {
    'draft': 'Bản nháp',
    'active': 'Đang hiệu lực',
    'expired': 'Đã hết hạn',
    'terminated': 'Đã chấm dứt',
    'pending': 'Chờ xử lý'
  },
  verificationStatuses: {
    'unverified': 'Chưa xác minh',
    'pending': 'Đang xác minh',
    'verified': 'Đã xác minh',
    'rejected': 'Bị từ chối'
  }
};

interface ReferenceState {
  // Data
  amenities: Amenity[];
  costTypes: CostType[];
  rules: Rule[];
  enums: AppEnums | null;
  
  // Loading states
  isLoading: boolean;
  isLoaded: boolean;
  error: string | null;
  
  // Actions
  loadReferenceData: () => Promise<void>;
  getAmenitiesByCategory: (category?: string) => Amenity[];
  getCostTypesByCategory: (category?: string) => CostType[];
  getRulesByCategory: (category?: string) => Rule[];
  translateEnum: (enumType: string, value: string) => string;
  clearError: () => void;
}

export const useReferenceStore = create<ReferenceState>()(
  persist(
    (set, get) => ({
      // Initial state
      amenities: [],
      costTypes: [],
      rules: [],
      enums: null,
      isLoading: false,
      isLoaded: false,
      error: null,

      // Load all reference data
      loadReferenceData: async () => {
        const state = get();
        if (state.isLoaded || state.isLoading) return;

        set({ isLoading: true, error: null });

        try {
          // Load all reference data in parallel using server actions
          const [amenities, costTypes, rules, enums] = await Promise.all([
            getAmenities(),
            getCostTypes(),
            getRules(),
            getAppEnums()
          ]);

          set({
            amenities: amenities || [],
            costTypes: costTypes || [],
            rules: rules || [],
            enums: enums || null,
            isLoading: false,
            isLoaded: true,
            error: null
          });
        } catch (error: unknown) {
          const errorMessage = error instanceof Error
            ? error.message
            : 'Failed to load reference data';
          set({
            isLoading: false,
            error: errorMessage
          });
          console.error('Failed to load reference data:', error);
        }
      },

      // Get amenities by category
      getAmenitiesByCategory: (category?: string) => {
        const { amenities } = get();
        if (!category) return amenities;
        return amenities.filter(amenity => amenity.category === category);
      },

      // Get cost types by category
      getCostTypesByCategory: (category?: string) => {
        const { costTypes } = get();
        if (!category) return costTypes;
        return costTypes.filter(costType => costType.category === category);
      },

      // Get rules by category
      getRulesByCategory: (category?: string) => {
        const { rules } = get();
        if (!category) return rules;
        return rules.filter(rule => rule.category === category);
      },

      // Translate enum value to Vietnamese
      translateEnum: (enumType: string, value: string) => {
        const translations = enumTranslations[enumType];
        return translations?.[value] || value;
      },

      // Clear error
      clearError: () => set({ error: null })
    }),
    {
      name: 'reference-storage',
      // Persist all data except loading states
      partialize: (state) => ({
        amenities: state.amenities,
        costTypes: state.costTypes,
        rules: state.rules,
        enums: state.enums,
        isLoaded: state.isLoaded
      })
    }
  )
);
